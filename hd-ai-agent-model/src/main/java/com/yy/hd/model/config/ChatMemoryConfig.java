package com.yy.hd.model.config;

import com.yy.hd.model.memory.AiChatMemoryRepository;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import org.springframework.ai.chat.memory.repository.jdbc.JdbcChatMemoryRepository;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

@Configuration
public class ChatMemoryConfig {

    @Bean
    public ChatMemoryRepository chatMemoryRepository(JdbcTemplate jdbcTemplate, JdbcChatMemoryRepository jdbcChatMemoryRepository) {
        return new AiChatMemoryRepository(jdbcTemplate, jdbcChatMemoryRepository);
    }
}
