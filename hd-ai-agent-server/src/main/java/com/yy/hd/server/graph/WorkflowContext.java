package com.yy.hd.server.graph;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Builder
@Data
public class WorkflowContext {

    private String uid;

    private String chatId;

    /**
     * 调用来源/场景
     */
    private String source;

    /**
     * 用户输入
     */
    private String input;

    /**
     * BM25关键词搜索上下文
     */
    private BM25SearchContext bm25SearchContext;

    /**
     * 向量搜索上下文
     */
    private SimilaritySearchContext similaritySearchContext;

    /**
     * 重排上下文
     */
    private RankingContext rankingContext;

    /**
     * 搜索/重排 key，用作业务隔离或者场景隔离
     */
    private String rankingKey;

    /**
     * 工具列表
     */
    private List<String> toolNames;

    /**
     * 大模型提供商，例如 mistral
     */
    private String provider;

    /**
     * 模型名称，例如 mistral-small-latest
     */
    private String model;

    /**
     * 是否是深度思考模式
     */
    private Boolean thinkingEnabled;

    /**
     * 下一个节点
     */
    private String nextNodeId;
}
