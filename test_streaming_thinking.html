<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>流式思维链测试</title>
  <script src="https://cdn.jsdelivr.net/npm/marked@4.0.18/marked.min.js"></script>
  <style>
    :root {
      --bg-primary: #0a0a0a;
      --bg-secondary: #111111;
      --bg-tertiary: #1a1a1a;
      --bg-hover: #222222;
      --border-primary: #2a2a2a;
      --border-secondary: #333333;
      --text-primary: #ffffff;
      --text-secondary: #b3b3b3;
      --text-tertiary: #888888;
      --accent-primary: #3b82f6;
      --accent-hover: #2563eb;
      --accent-light: rgba(59, 130, 246, 0.1);
      --radius-sm: 0.375rem;
      --radius-md: 0.5rem;
      --radius-lg: 0.75rem;
      --radius-xl: 1rem;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
      background: var(--bg-primary);
      color: var(--text-primary);
      margin: 0;
      padding: 2rem;
    }

    .test-container {
      max-width: 800px;
      margin: 0 auto;
    }

    .message-bubble {
      background: var(--bg-tertiary);
      border-radius: var(--radius-xl);
      padding: 1.5rem;
      margin: 1rem 0;
      border-bottom-left-radius: var(--radius-sm);
    }

    .thinking-chain {
      background: var(--bg-secondary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-lg);
      margin: 1rem 0;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .thinking-chain.thinking {
      border-color: var(--accent-primary);
      box-shadow: 0 0 0 1px var(--accent-light);
      animation: thinking-pulse 2s ease-in-out infinite;
    }

    .thinking-chain-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.875rem 1.25rem;
      cursor: pointer;
      background: var(--bg-tertiary);
      border-bottom: 1px solid var(--border-secondary);
      transition: all 0.2s ease;
      user-select: none;
    }

    .thinking-chain.thinking .thinking-chain-header {
      background: var(--accent-light);
    }

    .thinking-chain-header:hover {
      background: var(--bg-hover);
    }

    .thinking-chain-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--text-secondary);
    }

    .thinking-chain.thinking .thinking-chain-title {
      color: var(--accent-primary);
    }

    .thinking-chain-icon {
      width: 16px;
      height: 16px;
      transition: transform 0.2s ease;
      color: var(--accent-primary);
    }

    .thinking-chain-header.expanded .thinking-chain-icon {
      transform: rotate(90deg);
    }

    .thinking-chain-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
    }

    .thinking-chain-content.expanded {
      max-height: 1000px;
    }

    .thinking-chain-inner {
      padding: 1.25rem;
      color: var(--text-tertiary);
      font-size: 0.875rem;
      line-height: 1.6;
      white-space: pre-wrap;
      word-wrap: break-word;
    }

    .thinking-chain-inner.typing::after {
      content: '▋';
      animation: typing-cursor 1s infinite;
      color: var(--accent-primary);
    }

    .thinking-chain-badge {
      background: var(--accent-light);
      color: var(--accent-primary);
      padding: 0.25rem 0.5rem;
      border-radius: var(--radius-sm);
      font-size: 0.75rem;
      font-weight: 500;
    }

    .thinking-chain.thinking .thinking-chain-badge {
      animation: thinking-badge-pulse 1.5s ease-in-out infinite;
    }

    @keyframes thinking-pulse {
      0%, 100% { box-shadow: 0 0 0 1px var(--accent-light); }
      50% { box-shadow: 0 0 0 3px var(--accent-light); }
    }

    @keyframes thinking-badge-pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.6; }
    }

    @keyframes typing-cursor {
      0%, 50% { opacity: 1; }
      51%, 100% { opacity: 0; }
    }

    button {
      padding: 10px 20px;
      background: var(--accent-primary);
      color: white;
      border: none;
      border-radius: var(--radius-md);
      cursor: pointer;
      margin: 0.5rem;
    }

    button:hover {
      background: var(--accent-hover);
    }

    .controls {
      margin: 1rem 0;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>流式思维链渲染测试</h1>
    
    <div class="controls">
      <button onclick="startStreamingTest()">开始流式测试</button>
      <button onclick="resetTest()">重置</button>
    </div>
    
    <div class="message-bubble" id="testMessage">
      <div class="markdown-content" id="messageContent">
        <!-- 流式内容将在这里显示 -->
      </div>
    </div>
  </div>

  <script>
    // 配置 marked
    marked.setOptions({
      breaks: true,
      gfm: true
    });

    // 模拟的流式内容
    const streamingContent = `这是一个普通的回答开始。

<think>
让我仔细思考一下这个问题...

首先，我需要分析用户的需求：
1. 用户想要了解什么？
2. 我应该从哪个角度来回答？
3. 需要提供什么样的信息？

经过分析，我认为应该从以下几个方面来回答：
- 基本概念的解释
- 实际应用的例子
- 未来发展的趋势

这样的回答结构会比较清晰和全面。
</think>

基于我的分析，我的建议是：这是一个很好的问题！

<think>
再补充一些思考...
这个回答是否完整？
是否需要更多的细节说明？
我觉得还可以添加一些具体的例子。
</think>

希望这个回答对您有帮助。如果您还有其他问题，请随时告诉我。`;

    // 流式状态
    let streamingState = {
      isInThinking: false,
      currentThinkingId: null,
      thinkingContent: '',
      thinkingStartIndex: -1
    };

    let currentIndex = 0;
    let streamingInterval = null;

    function toggleThinkingChain(id) {
      const header = document.querySelector(`[data-thinking-id="${id}"] .thinking-chain-header`);
      const content = document.getElementById(`${id}-content`);
      const badge = header.querySelector('.thinking-chain-badge');
      
      if (!header || !content || !badge) return;
      
      const isExpanded = header.classList.contains('expanded');
      
      if (isExpanded) {
        header.classList.remove('expanded');
        content.classList.remove('expanded');
        badge.textContent = '展开查看';
      } else {
        header.classList.add('expanded');
        content.classList.add('expanded');
        badge.textContent = '收起';
      }
    }

    function createStreamingThinkingChainHtml(id) {
      return `
        <div class="thinking-chain thinking" data-thinking-id="${id}">
          <div class="thinking-chain-header" onclick="toggleThinkingChain('${id}')">
            <div class="thinking-chain-title">
              <svg class="thinking-chain-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
              <span>思维过程</span>
              <span class="thinking-chain-badge">思考中...</span>
            </div>
          </div>
          <div class="thinking-chain-content expanded" id="${id}-content">
            <div class="thinking-chain-inner typing" id="${id}-inner"></div>
          </div>
        </div>
      `;
    }

    function processStreamingContent(content) {
      const thinkStartRegex = /<think>/g;
      const thinkEndRegex = /<\/think>/g;
      
      let currentPos = 0;
      let result = '';
      
      while (currentPos < content.length) {
        if (!streamingState.isInThinking) {
          const thinkStartMatch = thinkStartRegex.exec(content.substring(currentPos));
          if (thinkStartMatch) {
            const startIndex = currentPos + thinkStartMatch.index;
            if (startIndex > currentPos) {
              result += content.substring(currentPos, startIndex);
            }
            
            streamingState.isInThinking = true;
            streamingState.thinkingStartIndex = startIndex + thinkStartMatch[0].length;
            streamingState.currentThinkingId = `thinking-chain-streaming-${Date.now()}`;
            streamingState.thinkingContent = '';
            
            result += createStreamingThinkingChainHtml(streamingState.currentThinkingId);
            
            currentPos = streamingState.thinkingStartIndex;
            thinkStartRegex.lastIndex = 0;
          } else {
            result += content.substring(currentPos);
            break;
          }
        } else {
          const thinkEndMatch = thinkEndRegex.exec(content.substring(currentPos));
          if (thinkEndMatch) {
            const endIndex = currentPos + thinkEndMatch.index;
            const thinkingContent = content.substring(streamingState.thinkingStartIndex, endIndex);
            streamingState.thinkingContent = thinkingContent;
            
            streamingState.isInThinking = false;
            currentPos = endIndex + thinkEndMatch[0].length;
            thinkEndRegex.lastIndex = 0;
          } else {
            const thinkingContent = content.substring(streamingState.thinkingStartIndex);
            streamingState.thinkingContent = thinkingContent;
            break;
          }
        }
      }
      
      return result;
    }

    function updateDOM(content) {
      const processedContent = processStreamingContent(content);
      const parts = processedContent.split(/(<div class="thinking-chain[^>]*>[\s\S]*?<\/div>)/);
      let htmlContent = '';
      
      for (let i = 0; i < parts.length; i++) {
        const part = parts[i];
        if (part.includes('thinking-chain')) {
          htmlContent += part;
        } else if (part.trim()) {
          htmlContent += marked.parse(part);
        }
      }
      
      document.getElementById('messageContent').innerHTML = htmlContent;
      
      if (streamingState.isInThinking && streamingState.currentThinkingId) {
        const thinkingInner = document.getElementById(`${streamingState.currentThinkingId}-inner`);
        if (thinkingInner) {
          thinkingInner.textContent = streamingState.thinkingContent;
        }
      }
      
      if (!streamingState.isInThinking && streamingState.currentThinkingId) {
        const thinkingChain = document.querySelector(`[data-thinking-id="${streamingState.currentThinkingId}"]`);
        const badge = thinkingChain?.querySelector('.thinking-chain-badge');
        const thinkingInner = thinkingChain?.querySelector('.thinking-chain-inner');
        if (thinkingChain && badge) {
          thinkingChain.classList.remove('thinking');
          badge.textContent = '展开查看';
          if (thinkingInner) {
            thinkingInner.classList.remove('typing');
          }
          const header = thinkingChain.querySelector('.thinking-chain-header');
          const content = thinkingChain.querySelector('.thinking-chain-content');
          if (header && content) {
            header.classList.remove('expanded');
            content.classList.remove('expanded');
          }
        }
      }
    }

    function startStreamingTest() {
      resetTest();
      currentIndex = 0;
      
      streamingInterval = setInterval(() => {
        if (currentIndex < streamingContent.length) {
          currentIndex += Math.random() * 3 + 1; // 随机速度
          if (currentIndex > streamingContent.length) {
            currentIndex = streamingContent.length;
          }
          
          const currentContent = streamingContent.substring(0, currentIndex);
          updateDOM(currentContent);
          
          if (currentIndex >= streamingContent.length) {
            clearInterval(streamingInterval);
          }
        }
      }, 50);
    }

    function resetTest() {
      if (streamingInterval) {
        clearInterval(streamingInterval);
      }
      currentIndex = 0;
      streamingState = {
        isInThinking: false,
        currentThinkingId: null,
        thinkingContent: '',
        thinkingStartIndex: -1
      };
      document.getElementById('messageContent').innerHTML = '';
    }

    // 页面加载时显示初始状态
    window.onload = function() {
      document.getElementById('messageContent').innerHTML = '<p>点击"开始流式测试"查看效果</p>';
    };
  </script>
</body>
</html>
