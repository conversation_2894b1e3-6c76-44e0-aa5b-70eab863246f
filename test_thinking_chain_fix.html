<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>思维链渲染修复测试</title>
  <script src="https://cdn.jsdelivr.net/npm/marked@4.0.18/marked.min.js"></script>
  <style>
    :root {
      --bg-primary: #0a0a0a;
      --bg-secondary: #111111;
      --bg-tertiary: #1a1a1a;
      --bg-hover: #222222;
      --border-primary: #2a2a2a;
      --border-secondary: #333333;
      --text-primary: #ffffff;
      --text-secondary: #b3b3b3;
      --text-tertiary: #888888;
      --accent-primary: #3b82f6;
      --accent-hover: #2563eb;
      --accent-light: rgba(59, 130, 246, 0.1);
      --radius-sm: 0.375rem;
      --radius-md: 0.5rem;
      --radius-lg: 0.75rem;
      --radius-xl: 1rem;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
      color: var(--text-primary);
      margin: 0;
      padding: 20px;
      line-height: 1.6;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
    }

    .test-section {
      background: var(--bg-tertiary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-lg);
      padding: 20px;
      margin-bottom: 20px;
    }

    .test-input {
      width: 100%;
      min-height: 150px;
      background: var(--bg-secondary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-md);
      color: var(--text-primary);
      padding: 15px;
      font-family: monospace;
      font-size: 14px;
      resize: vertical;
    }

    .test-button {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      padding: 10px 20px;
      font-weight: 600;
      cursor: pointer;
      margin: 10px 5px 0 0;
    }

    .test-button:hover {
      transform: translateY(-1px);
    }

    .result-area {
      background: var(--bg-secondary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-md);
      padding: 20px;
      margin-top: 15px;
      min-height: 100px;
    }

    /* 思维链样式 */
    .thinking-chain {
      background: var(--bg-secondary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-lg);
      margin: 1rem 0;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .thinking-chain-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.875rem 1.25rem;
      cursor: pointer;
      background: var(--bg-tertiary);
      border-bottom: 1px solid var(--border-secondary);
      transition: all 0.2s ease;
      user-select: none;
    }

    .thinking-chain-header:hover {
      background: var(--bg-hover);
    }

    .thinking-chain-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--text-secondary);
    }

    .thinking-chain-icon {
      width: 16px;
      height: 16px;
      transition: transform 0.2s ease;
      color: var(--accent-primary);
    }

    .thinking-chain-header.expanded .thinking-chain-icon {
      transform: rotate(90deg);
    }

    .thinking-chain-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
    }

    .thinking-chain-content.expanded {
      max-height: 1000px;
    }

    .thinking-chain-inner {
      padding: 1.25rem;
      color: var(--text-tertiary);
      font-size: 0.875rem;
      line-height: 1.6;
      white-space: pre-wrap;
      word-wrap: break-word;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    }

    .thinking-chain-badge {
      background: var(--accent-light);
      color: var(--accent-primary);
      padding: 0.25rem 0.5rem;
      border-radius: var(--radius-sm);
      font-size: 0.75rem;
      font-weight: 500;
    }

    /* 思维链动画效果 */
    .thinking-chain.thinking {
      border-color: var(--accent-primary);
      box-shadow: 0 0 0 1px var(--accent-light);
      animation: thinking-pulse 2s ease-in-out infinite;
    }

    .thinking-chain.thinking .thinking-chain-header {
      background: var(--accent-light);
    }

    .thinking-chain.thinking .thinking-chain-title {
      color: var(--accent-primary);
    }

    .thinking-chain.thinking .thinking-chain-badge {
      animation: thinking-badge-pulse 1.5s ease-in-out infinite;
    }

    @keyframes thinking-pulse {
      0%, 100% {
        box-shadow: 0 0 0 1px var(--accent-light);
      }
      50% {
        box-shadow: 0 0 0 3px var(--accent-light);
      }
    }

    @keyframes thinking-badge-pulse {
      0%, 100% {
        opacity: 1;
      }
      50% {
        opacity: 0.6;
      }
    }

    /* 思维链内容的打字效果 */
    .thinking-chain-inner.typing::after {
      content: '▋';
      animation: typing-cursor 1s infinite;
      color: var(--accent-primary);
    }

    @keyframes typing-cursor {
      0%, 50% {
        opacity: 1;
      }
      51%, 100% {
        opacity: 0;
      }
    }

    .markdown-content p {
      margin-bottom: 1rem;
    }

    .markdown-content code {
      background: var(--bg-secondary);
      color: var(--accent-primary);
      padding: 0.125rem 0.375rem;
      border-radius: var(--radius-sm);
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
      font-size: 0.875em;
    }

    .status {
      padding: 10px;
      border-radius: var(--radius-md);
      margin: 10px 0;
      font-size: 14px;
    }

    .status.info {
      background: rgba(59, 130, 246, 0.1);
      border: 1px solid var(--accent-primary);
      color: var(--accent-primary);
    }

    .status.success {
      background: rgba(16, 185, 129, 0.1);
      border: 1px solid #10b981;
      color: #10b981;
    }

    .status.error {
      background: rgba(239, 68, 68, 0.1);
      border: 1px solid #ef4444;
      color: #ef4444;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>思维链渲染修复测试</h1>
    
    <div class="test-section">
      <h3>测试内容输入：</h3>
      <textarea class="test-input" id="testInput" placeholder="输入包含 <think>...</think> 标签的内容">这是一个普通的回答内容。

<think>
让我思考一下这个问题...

首先，我需要分析：
1. 用户的真实需求是什么？
2. 我应该从哪个角度回答？
3. 需要提供什么样的信息？

经过思考，我认为应该这样回答...
</think>

基于我的分析，我的建议是：这是一个很好的问题！

<think>
再补充一些思考...
这个回答是否完整？
是否需要更多的细节说明？
</think>

希望这个回答对您有帮助。</textarea>
      
      <button class="test-button" onclick="testStaticRendering()">测试静态渲染</button>
      <button class="test-button" onclick="testStreamingRendering()">测试流式渲染</button>
      <button class="test-button" onclick="clearResults()">清空结果</button>
      
      <div id="status" class="status info" style="display: none;"></div>
    </div>

    <div class="test-section">
      <h3>渲染结果：</h3>
      <div class="result-area" id="resultArea">
        <p style="color: var(--text-tertiary);">点击上方按钮开始测试...</p>
      </div>
    </div>
  </div>

  <script>
    // 配置 marked
    marked.setOptions({
      breaks: true,
      gfm: true
    });

    // 模拟流式思维链状态
    const streamingState = {
      isInThinking: false,
      currentThinkingId: null,
      thinkingContent: '',
      thinkingStartIndex: -1
    };

    function showStatus(message, type = 'info') {
      const statusEl = document.getElementById('status');
      statusEl.textContent = message;
      statusEl.className = `status ${type}`;
      statusEl.style.display = 'block';
      
      if (type === 'success' || type === 'error') {
        setTimeout(() => {
          statusEl.style.display = 'none';
        }, 3000);
      }
    }

    function clearResults() {
      document.getElementById('resultArea').innerHTML = '<p style="color: var(--text-tertiary);">点击上方按钮开始测试...</p>';
      document.getElementById('status').style.display = 'none';
    }

    // 静态渲染测试
    function testStaticRendering() {
      showStatus('开始静态渲染测试...', 'info');

      const content = document.getElementById('testInput').value;
      const processedContent = processThinkingChain(content);

      const resultArea = document.getElementById('resultArea');
      resultArea.innerHTML = `<div class="markdown-content">${processedContent}</div>`;

      // 绑定点击事件
      resultArea.querySelectorAll('.thinking-chain-header').forEach(header => {
        const thinkingId = header.closest('.thinking-chain').getAttribute('data-thinking-id');
        if (thinkingId) {
          header.onclick = () => toggleThinkingChain(thinkingId);
        }
      });

      showStatus('静态渲染完成！', 'success');
    }

    // 流式渲染测试
    function testStreamingRendering() {
      showStatus('开始流式渲染测试...', 'info');

      const content = document.getElementById('testInput').value;
      const resultArea = document.getElementById('resultArea');

      // 重置状态
      resetStreamingThinkingState();

      // 清空结果区域
      resultArea.innerHTML = '';

      // 模拟流式输出
      let currentIndex = 0;
      const chunkSize = 5; // 每次输出5个字符

      function outputNextChunk() {
        if (currentIndex >= content.length) {
          // 完成流式输出
          if (streamingState.isInThinking) {
            completeStreamingThinkingChain();
          }
          resetStreamingThinkingState();
          showStatus('流式渲染完成！', 'success');
          return;
        }

        const chunk = content.substring(0, currentIndex + chunkSize);
        currentIndex += chunkSize;

        // 处理流式内容
        const processedResult = processThinkingChainStreaming(chunk);
        updateStreamingDOM(resultArea, processedResult);

        // 继续下一块
        setTimeout(outputNextChunk, 100);
      }

      outputNextChunk();
    }

    // 处理思维链内容（静态版本）
    function processThinkingChain(content) {
      if (!content) return '';

      const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
      let processedContent = content;
      let match;
      let thinkingChainId = 0;
      const replacements = [];

      while ((match = thinkRegex.exec(content)) !== null) {
        const thinkingContent = match[1].trim();
        const thinkingChainHtml = createThinkingChainHtml(thinkingContent, thinkingChainId++);
        replacements.push({
          original: match[0],
          replacement: thinkingChainHtml,
          index: match.index
        });
      }

      replacements.reverse().forEach(replacement => {
        processedContent = processedContent.substring(0, replacement.index) +
                          replacement.replacement +
                          processedContent.substring(replacement.index + replacement.original.length);
      });

      if (replacements.length === 0) {
        return marked.parse(processedContent);
      }

      const thinkingChainPlaceholder = '___THINKING_CHAIN_PLACEHOLDER___';
      const thinkingChains = [];
      let placeholderIndex = 0;

      processedContent = processedContent.replace(/<div class="thinking-chain"[\s\S]*?<\/div>/g, (match) => {
        thinkingChains.push(match);
        return `${thinkingChainPlaceholder}${placeholderIndex++}${thinkingChainPlaceholder}`;
      });

      let result = '';
      try {
        result = marked.parse(processedContent);
      } catch (error) {
        console.warn('Markdown parsing error:', error);
        result = processedContent.replace(/\n/g, '<br>');
      }

      thinkingChains.forEach((chain, index) => {
        const placeholder = `${thinkingChainPlaceholder}${index}${thinkingChainPlaceholder}`;
        result = result.replace(placeholder, chain);
      });

      return result;
    }

    // 优化的流式思维链处理函数
    function processThinkingChainStreaming(content) {
      if (content.length < streamingState.thinkingContent.length) {
        resetStreamingThinkingState();
      }

      const completeThinkRegex = /<think>([\s\S]*?)<\/think>/g;
      const incompleteThinkRegex = /<think>([\s\S]*)$/;

      let result = '';
      let lastIndex = 0;
      let match;
      let thinkingChainId = 0;

      while ((match = completeThinkRegex.exec(content)) !== null) {
        if (match.index > lastIndex) {
          result += content.substring(lastIndex, match.index);
        }

        const thinkingContent = match[1].trim();
        const uniqueId = `thinking-chain-complete-${Date.now()}-${thinkingChainId++}`;
        result += createCompletedThinkingChainHtml(thinkingContent, uniqueId);

        lastIndex = match.index + match[0].length;
      }

      const remainingContent = content.substring(lastIndex);
      const incompleteMatch = incompleteThinkRegex.exec(remainingContent);

      if (incompleteMatch) {
        const beforeThink = remainingContent.substring(0, incompleteMatch.index);
        if (beforeThink) {
          result += beforeThink;
        }

        const partialThinkingContent = incompleteMatch[1];

        if (!streamingState.isInThinking ||
            streamingState.thinkingContent !== partialThinkingContent) {

          if (!streamingState.isInThinking) {
            streamingState.isInThinking = true;
            streamingState.currentThinkingId = `thinking-chain-streaming-${Date.now()}`;
          }

          streamingState.thinkingContent = partialThinkingContent;
          result += createStreamingThinkingChainHtml(streamingState.currentThinkingId);
        }
      } else {
        if (remainingContent) {
          result += remainingContent;
        }

        if (streamingState.isInThinking) {
          streamingState.isInThinking = false;
        }
      }

      return result;
    }

    // 创建思维链HTML
    function createThinkingChainHtml(content, id) {
      const uniqueId = `thinking-chain-${Date.now()}-${id}`;
      const processedContent = processThinkingContent(content);
      return `
        <div class="thinking-chain" data-thinking-id="${uniqueId}">
          <div class="thinking-chain-header">
            <div class="thinking-chain-title">
              <svg class="thinking-chain-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
              <span>思维过程</span>
              <span class="thinking-chain-badge">展开查看</span>
            </div>
          </div>
          <div class="thinking-chain-content" id="${uniqueId}-content">
            <div class="thinking-chain-inner">${processedContent}</div>
          </div>
        </div>
      `;
    }

    // 创建完成的思维链HTML
    function createCompletedThinkingChainHtml(content, id) {
      const processedContent = processThinkingContent(content);
      return `
        <div class="thinking-chain" data-thinking-id="${id}">
          <div class="thinking-chain-header">
            <div class="thinking-chain-title">
              <svg class="thinking-chain-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
              <span>思维过程</span>
              <span class="thinking-chain-badge">展开查看</span>
            </div>
          </div>
          <div class="thinking-chain-content" id="${id}-content">
            <div class="thinking-chain-inner">${processedContent}</div>
          </div>
        </div>
      `;
    }

    // 创建流式思维链HTML
    function createStreamingThinkingChainHtml(id) {
      return `
        <div class="thinking-chain thinking" data-thinking-id="${id}">
          <div class="thinking-chain-header">
            <div class="thinking-chain-title">
              <svg class="thinking-chain-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
              <span>思维过程</span>
              <span class="thinking-chain-badge">思考中...</span>
            </div>
          </div>
          <div class="thinking-chain-content expanded" id="${id}-content">
            <div class="thinking-chain-inner typing" id="${id}-inner"></div>
          </div>
        </div>
      `;
    }

    // 处理思维链内容
    function processThinkingContent(content) {
      if (!content) return '';
      const div = document.createElement('div');
      div.textContent = content;
      const escaped = div.innerHTML;
      return escaped.replace(/\n/g, '<br>');
    }

    // 更新流式DOM
    function updateStreamingDOM(bubbleElement, processedContent) {
      if (!processedContent || processedContent.trim() === '') {
        return;
      }

      const thinkingChainPlaceholder = '___THINKING_CHAIN_PLACEHOLDER___';
      const thinkingChains = [];
      let placeholderIndex = 0;

      let contentWithPlaceholders = processedContent.replace(/<div class="thinking-chain[^>]*>[\s\S]*?<\/div>/g, (match) => {
        thinkingChains.push(match);
        return `${thinkingChainPlaceholder}${placeholderIndex++}${thinkingChainPlaceholder}`;
      });

      let htmlContent = '';
      if (contentWithPlaceholders.trim()) {
        try {
          htmlContent = marked.parse(contentWithPlaceholders);
        } catch (error) {
          console.warn('Markdown parsing error:', error);
          htmlContent = contentWithPlaceholders.replace(/\n/g, '<br>');
        }
      }

      thinkingChains.forEach((chain, index) => {
        const placeholder = `${thinkingChainPlaceholder}${index}${thinkingChainPlaceholder}`;
        htmlContent = htmlContent.replace(placeholder, chain);
      });

      const hasVisibleContent = htmlContent.trim() || thinkingChains.length > 0;

      if (hasVisibleContent) {
        bubbleElement.innerHTML = `<div class="markdown-content">${htmlContent}</div>`;

        bubbleElement.querySelectorAll('.thinking-chain-header').forEach(header => {
          const thinkingId = header.closest('.thinking-chain').getAttribute('data-thinking-id');
          if (thinkingId) {
            header.onclick = () => toggleThinkingChain(thinkingId);
          }
        });

        updateStreamingThinkingContent();
      }
    }

    // 更新流式思维链内容
    function updateStreamingThinkingContent() {
      if (streamingState.isInThinking && streamingState.currentThinkingId) {
        const thinkingInner = document.getElementById(`${streamingState.currentThinkingId}-inner`);
        if (thinkingInner) {
          thinkingInner.innerHTML = processThinkingContent(streamingState.thinkingContent);

          if (!thinkingInner.classList.contains('typing')) {
            thinkingInner.classList.add('typing');
          }
        }
      }
    }

    // 完成流式思维链
    function completeStreamingThinkingChain() {
      if (streamingState.currentThinkingId) {
        const thinkingChain = document.querySelector(`[data-thinking-id="${streamingState.currentThinkingId}"]`);
        const badge = thinkingChain?.querySelector('.thinking-chain-badge');
        const thinkingInner = thinkingChain?.querySelector('.thinking-chain-inner');

        if (thinkingChain && badge) {
          thinkingChain.classList.remove('thinking');
          badge.textContent = '展开查看';

          if (thinkingInner) {
            thinkingInner.classList.remove('typing');
          }

          const header = thinkingChain.querySelector('.thinking-chain-header');
          const content = thinkingChain.querySelector('.thinking-chain-content');
          if (header && content) {
            header.classList.remove('expanded');
            content.classList.remove('expanded');
          }
        }
      }
    }

    // 重置流式思维链状态
    function resetStreamingThinkingState() {
      if (streamingState.isInThinking) {
        completeStreamingThinkingChain();
      }

      streamingState.isInThinking = false;
      streamingState.currentThinkingId = null;
      streamingState.thinkingContent = '';
      streamingState.thinkingStartIndex = -1;
    }

    // 切换思维链展开/折叠状态
    function toggleThinkingChain(id) {
      const header = document.querySelector(`[data-thinking-id="${id}"] .thinking-chain-header`);
      const content = document.getElementById(`${id}-content`);
      const badge = header?.querySelector('.thinking-chain-badge');

      if (!header || !content || !badge) return;

      const isExpanded = header.classList.contains('expanded');

      if (isExpanded) {
        header.classList.remove('expanded');
        content.classList.remove('expanded');
        badge.textContent = '展开查看';
      } else {
        header.classList.add('expanded');
        content.classList.add('expanded');
        badge.textContent = '收起';
      }
    }
  </script>
</body>
</html>
