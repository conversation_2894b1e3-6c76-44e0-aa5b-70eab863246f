<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>思维链修复测试</title>
    <style>
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e9ecef;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --text-tertiary: #adb5bd;
            --border-primary: #dee2e6;
            --border-secondary: #e9ecef;
            --accent-primary: #007bff;
            --accent-light: #e3f2fd;
            --radius-sm: 4px;
            --radius-lg: 8px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        h1 {
            color: var(--text-primary);
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
        }

        /* 思维链样式 - 参考grok.com风格 */
        .thinking-chain {
            background: var(--bg-secondary);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-lg);
            margin: 1rem 0;
            overflow: hidden;
        }

        .thinking-chain-header {
            padding: 1rem 1.25rem;
            cursor: pointer;
            user-select: none;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .thinking-chain-header:hover {
            background: var(--bg-tertiary);
        }

        .thinking-chain-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .thinking-chain-icon {
            width: 1rem;
            height: 1rem;
            transition: transform 0.2s ease;
            color: var(--text-tertiary);
        }

        .thinking-chain-header.expanded .thinking-chain-icon {
            transform: rotate(90deg);
        }

        .thinking-chain-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .thinking-chain-content.expanded {
            max-height: 400px; /* 设置合理的最大高度 */
            overflow-y: auto; /* 添加垂直滚动条 */
        }

        .thinking-chain-inner {
            padding: 1.25rem;
            color: var(--text-tertiary);
            font-size: 0.875rem;
            line-height: 1.6;
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow-wrap: break-word;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
        }

        /* 思维链滚动条样式 */
        .thinking-chain-content::-webkit-scrollbar {
            width: 6px;
        }

        .thinking-chain-content::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
            border-radius: 3px;
        }

        .thinking-chain-content::-webkit-scrollbar-thumb {
            background: var(--border-secondary);
            border-radius: 3px;
        }

        .thinking-chain-content::-webkit-scrollbar-thumb:hover {
            background: var(--text-tertiary);
        }

        .thinking-chain-badge {
            background: var(--accent-light);
            color: var(--accent-primary);
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .test-button {
            background: var(--accent-primary);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: var(--radius-sm);
            cursor: pointer;
            margin: 10px 5px;
        }

        .test-button:hover {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>思维链修复测试</h1>
        
        <div class="test-section">
            <h2>测试1: 短内容思维链</h2>
            <div id="short-content"></div>
            <button class="test-button" onclick="testShortContent()">测试短内容</button>
        </div>

        <div class="test-section">
            <h2>测试2: 长内容思维链（测试滚动）</h2>
            <div id="long-content"></div>
            <button class="test-button" onclick="testLongContent()">测试长内容</button>
        </div>

        <div class="test-section">
            <h2>测试3: 包含HTML标签的内容</h2>
            <div id="html-content"></div>
            <button class="test-button" onclick="testHtmlContent()">测试HTML内容</button>
        </div>
    </div>

    <script>
        // HTML转义函数
        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 安全地处理思维链内容，保留换行但转义HTML
        function processThinkingContent(content) {
            if (!content) return '';
            // 先转义HTML标签，但保留换行符
            const escaped = escapeHtml(content);
            // 将换行符转换为<br>标签，确保正确显示
            const withBreaks = escaped.replace(/\n/g, '<br>');
            return withBreaks;
        }

        // 创建思维链HTML
        function createThinkingChainHtml(content, id) {
            const uniqueId = `thinking-chain-${Date.now()}-${id}`;
            const processedContent = processThinkingContent(content);
            return `
                <div class="thinking-chain" data-thinking-id="${uniqueId}">
                    <div class="thinking-chain-header">
                        <div class="thinking-chain-title">
                            <svg class="thinking-chain-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                            <span>思维过程</span>
                            <span class="thinking-chain-badge">展开查看</span>
                        </div>
                    </div>
                    <div class="thinking-chain-content" id="${uniqueId}-content">
                        <div class="thinking-chain-inner">${processedContent}</div>
                    </div>
                </div>
            `;
        }

        // 切换思维链展开/折叠状态
        function toggleThinkingChain(id) {
            const header = document.querySelector(`[data-thinking-id="${id}"] .thinking-chain-header`);
            const content = document.getElementById(`${id}-content`);
            const badge = header.querySelector('.thinking-chain-badge');

            if (header.classList.contains('expanded')) {
                header.classList.remove('expanded');
                content.classList.remove('expanded');
                badge.textContent = '展开查看';
            } else {
                header.classList.add('expanded');
                content.classList.add('expanded');
                badge.textContent = '收起';
            }
        }

        // 测试短内容
        function testShortContent() {
            const content = "这是一个简单的思维过程。\n包含换行符。\n测试HTML转义：<script>alert('test')</script>";
            const html = createThinkingChainHtml(content, 1);
            document.getElementById('short-content').innerHTML = html;
            
            // 绑定点击事件
            const header = document.querySelector('#short-content .thinking-chain-header');
            const thinkingId = header.closest('.thinking-chain').getAttribute('data-thinking-id');
            header.onclick = () => toggleThinkingChain(thinkingId);
        }

        // 测试长内容
        function testLongContent() {
            const content = `这是一个很长的思维过程，用来测试滚动功能。

第一段：我需要分析这个问题的各个方面。首先，让我考虑用户的需求是什么。用户希望看到一个能够正确显示思维链的界面，并且当内容过长时能够滚动查看。

第二段：接下来，我需要考虑技术实现。我们使用了CSS的max-height和overflow-y属性来实现滚动功能。当内容超过400px高度时，会出现滚动条。

第三段：关于HTML标签的处理，我们使用escapeHtml函数来转义用户输入中的HTML标签，防止XSS攻击。同时，我们将换行符转换为<br>标签来保持格式。

第四段：用户体验方面，我们添加了自定义的滚动条样式，使其看起来更加美观。滚动条的宽度设置为6px，并且有hover效果。

第五段：测试方面，我们需要确保：
1. 短内容能正常显示
2. 长内容能正确滚动
3. HTML标签被正确转义
4. 换行符被正确处理
5. 点击展开/收起功能正常

第六段：这个测试内容应该足够长，能够触发滚动条的显示。让我们看看效果如何。

第七段：如果一切正常，用户应该能够看到一个带有滚动条的思维链区域，可以上下滚动查看完整内容。

第八段：最后，我们还需要确保在移动设备上也能正常工作，滚动体验应该是流畅的。`;

            const html = createThinkingChainHtml(content, 2);
            document.getElementById('long-content').innerHTML = html;
            
            // 绑定点击事件
            const header = document.querySelector('#long-content .thinking-chain-header');
            const thinkingId = header.closest('.thinking-chain').getAttribute('data-thinking-id');
            header.onclick = () => toggleThinkingChain(thinkingId);
        }

        // 测试HTML内容
        function testHtmlContent() {
            const content = `测试HTML标签转义：

1. 脚本标签：<script>alert('这应该被转义')</script>
2. 样式标签：<style>body{background:red}</style>
3. 链接标签：<a href="javascript:alert('test')">危险链接</a>
4. 图片标签：<img src="x" onerror="alert('xss')">
5. 普通HTML：<div><p>这些都应该被转义显示</p></div>

换行测试：
第一行
第二行
第三行

特殊字符：& < > " '`;

            const html = createThinkingChainHtml(content, 3);
            document.getElementById('html-content').innerHTML = html;
            
            // 绑定点击事件
            const header = document.querySelector('#html-content .thinking-chain-header');
            const thinkingId = header.closest('.thinking-chain').getAttribute('data-thinking-id');
            header.onclick = () => toggleThinkingChain(thinkingId);
        }
    </script>
</body>
</html>
